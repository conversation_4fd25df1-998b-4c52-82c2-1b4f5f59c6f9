// src/components/toolbars/SearchToolBar.tsx
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  MapPin,
  Search,
  Calendar,
  Users,
  Utensils,
  Soup,
  ShoppingBag,
} from "lucide-react";
import PlaceFilter from "./PlaceFilter"; // Import the new component

interface SearchToolbarProps {
  // Renamed props for clarity
  address: string; // Changed from 'search'
  onAddressChange: (
    address: string,
    lat?: number,
    lng?: number,
    bounds?: google.maps.LatLngBounds
  ) => void; // Changed from 'onSearchDestinations'
  isMapApiLoaded: boolean; // New prop
  googleMapsApiKey: string; // New prop
  libraries: ("places" | "drawing" | "geometry" | "visualization")[]; // New prop

  dishName: string;
  onDishSelect: (value: string) => void;
  name: string; // Keep 'name' if it refers to 'title'/'when'
  onSearchName: (value: string) => void; // Keep if it refers to 'dateFilter'
  offering: string;
  onDineInSelect: (value: string) => void;

  onSearchClick: () => void; // Keep the main search trigger
  isLoading?: boolean; // Add loading state
}

const HomeSearchToolbar: React.FC<SearchToolbarProps> = ({
  address,
  onAddressChange,
  isMapApiLoaded,
  googleMapsApiKey,
  libraries,
  dishName,
  name,
  offering,
  onSearchClick,
  onDishSelect,
  onSearchName,
  onDineInSelect,
  isLoading = false,
}) => {
  // Handle custom search trigger for manual text input
  const handleSearchTrigger = (query: string) => {
    // This triggers when user searches with custom text that wasn't autocompleted
    console.log("Custom search triggered for:", query);
    onSearchClick(); // Trigger the main search function
  };
  return (
    <div className="flex flex-col md:flex-row items-center justify-between gap-1 md:gap-2 bg-background p-2 rounded-full border border-border shadow-md w-full max-w-4xl mx-auto">
      {/* Place Filter replaces the old address input */}
      <div className="flex-grow w-full md:w-auto">
        <PlaceFilter
          initialValue={address}
          onPlaceSelect={onAddressChange}
          onSearchTrigger={handleSearchTrigger}
          isMapApiLoaded={isMapApiLoaded}
          googleMapsApiKey={googleMapsApiKey}
          libraries={libraries}
          placeholder="Where to?"
          allowCustomInput={true}
          disabled={isLoading}
        />
      </div>
      {/* Divider */}
      <div className="hidden md:block h-5 border-l border-gray-300"></div>

      {/* Other Filters (Dish Name, Date/Title, Offering) */}
      <div className="flex-grow w-full md:w-auto">
        <Input
          type="text"
          placeholder="Dish name..."
          value={dishName}
          onChange={(e) => onDishSelect(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && onSearchClick()}
          className="border-none focus-visible:ring-0 shadow-none text-sm h-8"
          disabled={isLoading}
          aria-label="Search by dish name"
          autoComplete="off"
        />
      </div>
      <div className="hidden md:block h-5 border-l border-gray-300"></div>

      {/* If 'name' is for 'when/title', keep it */}
      <div className="flex-grow w-full md:w-auto">
        <Input
          type="text"
          placeholder="When?"
          value={name}
          onChange={(e) => onSearchName(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && onSearchClick()}
          className="border-none focus-visible:ring-0 shadow-none text-sm h-8"
          disabled={isLoading}
          aria-label="Search by date or time"
          autoComplete="off"
        />
      </div>
      <div className="hidden md:block h-5 border-l border-gray-300"></div>

      {/* Offering Filter */}
      <div className="flex-grow w-full md:w-auto">
        <Input
          type="text"
          placeholder="Offering (Dine In, etc.)"
          value={offering}
          onChange={(e) => onDineInSelect(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && onSearchClick()}
          className="border-none focus-visible:ring-0 shadow-none text-sm h-8"
          disabled={isLoading}
          aria-label="Search by offering type"
          autoComplete="off"
        />
      </div>

      {/* Search Button */}
      <Button
        size="icon"
        className="rounded-full bg-primary text-primary-foreground w-8 h-8 flex-shrink-0"
        onClick={onSearchClick}
        aria-label="Search"
        disabled={isLoading}
      >
        {isLoading ? (
          <div className="h-4 w-4 border-2 border-t-transparent border-white rounded-full animate-spin" />
        ) : (
          <Search className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
};

export default HomeSearchToolbar;
